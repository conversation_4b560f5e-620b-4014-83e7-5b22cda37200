using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Identity.Application.Common.Interfaces
{
    public interface ISubscriptionIntegrationService
    {
        /// <summary>
        /// Gets user's subscription plan ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Subscription plan ID or null if no subscription</returns>
        Task<Guid?> GetUserSubscriptionPlanIdAsync(Guid userId);

        /// <summary>
        /// Gets feature IDs from PlanFeature table based on subscription plan ID
        /// </summary>
        /// <param name="subscriptionPlanId">Subscription plan ID</param>
        /// <returns>List of feature IDs</returns>
        Task<List<Guid>> GetPlanFeatureIdsAsync(Guid subscriptionPlanId);

        /// <summary>
        /// Gets feature details from FeatureFlag table based on feature IDs
        /// </summary>
        /// <param name="featureIds">List of feature IDs</param>
        /// <returns>Dictionary mapping feature ID to feature details</returns>
        Task<Dictionary<Guid, SubscriptionFeatureDetails>> GetFeatureFlagDetailsByIdsAsync(List<Guid> featureIds);
    }

    public class SubscriptionFeatureDetails
    {
        public Guid FeatureId { get; set; }
        public string FeatureName { get; set; } = string.Empty;
        public Guid? MenuId { get; set; }
    }
}
