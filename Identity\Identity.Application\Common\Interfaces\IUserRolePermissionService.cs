using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Identity.Application.Common.Models;
using Identity.Domain.Entities;

namespace Identity.Application.Common.Interfaces
{
    public interface IUserRolePermissionService
    {
        /// <summary>
        /// Gets all roles and permissions for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of roles with their permissions</returns>
        Task<List<RoleDto>> GetUserRolesWithPermissionsAsync(Guid userId);

        /// <summary>
        /// Gets roles and permissions based on user type - handles both subscription-based and role-based user types
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="userType">User type to determine logic path</param>
        /// <returns>List of roles with permissions</returns>
        Task<List<RoleDto>> GetUserRolesWithPermissionsByUserTypeAsync(Guid userId, UserType userType);

        /// <summary>
        /// Gets role IDs for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of role IDs</returns>
        Task<List<Guid>> GetUserRoleIdsAsync(Guid userId);

        /// <summary>
        /// Gets permission IDs for a role
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <returns>List of permission IDs</returns>
        Task<List<Guid>> GetRolePermissionIdsAsync(Guid roleId);

        /// <summary>
        /// Gets permission details by IDs
        /// </summary>
        /// <param name="permissionIds">List of permission IDs</param>
        /// <returns>List of permissions with their details</returns>
        Task<List<PermissionDto>> GetPermissionsByIdsAsync(List<Guid> permissionIds);

        /// <summary>
        /// Gets complete permission details by IDs including feature and menu information
        /// </summary>
        /// <param name="permissionIds">List of permission IDs</param>
        /// <returns>List of permissions with complete details</returns>
        Task<List<PermissionDto>> GetCompletePermissionsByIdsAsync(List<Guid> permissionIds);
    }
}
