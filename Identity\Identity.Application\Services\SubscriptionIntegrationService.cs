using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Text.Json;
using Identity.Application.Common.Interfaces;

namespace Identity.Application.Services
{
    public class SubscriptionIntegrationService : ISubscriptionIntegrationService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<SubscriptionIntegrationService> _logger;
        private readonly string _subscriptionServiceBaseUrl;

        public SubscriptionIntegrationService(
            HttpClient httpClient,
            ILogger<SubscriptionIntegrationService> logger,
            IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _subscriptionServiceBaseUrl = configuration.GetValue<string>("Services:SubscriptionService:BaseUrl") 
                ?? "https://localhost:7001"; // Default fallback
        }

        public async Task<Guid?> GetUserSubscriptionPlanIdAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Fetching subscription plan ID for user {UserId}", userId);

                var response = await _httpClient.GetAsync($"{_subscriptionServiceBaseUrl}/api/subscriptions/user/{userId}/plan-id");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<SubscriptionPlanResponse>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Successfully retrieved subscription plan ID {PlanId} for user {UserId}", 
                        result?.PlanId, userId);
                    
                    return result?.PlanId;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogInformation("No subscription found for user {UserId}", userId);
                    return null;
                }
                else
                {
                    _logger.LogWarning("Failed to get subscription plan ID for user {UserId}. Status: {StatusCode}", 
                        userId, response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching subscription plan ID for user {UserId}", userId);
                return null;
            }
        }

        public async Task<List<Guid>> GetPlanFeatureIdsAsync(Guid subscriptionPlanId)
        {
            try
            {
                _logger.LogInformation("Fetching feature IDs for subscription plan {PlanId}", subscriptionPlanId);

                var response = await _httpClient.GetAsync($"{_subscriptionServiceBaseUrl}/api/plans/{subscriptionPlanId}/features");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var features = JsonSerializer.Deserialize<List<PlanFeatureResponse>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    var featureIds = features?.Where(f => f.IsEnabled).Select(f => f.FeatureId).ToList() ?? new List<Guid>();
                    
                    _logger.LogInformation("Successfully retrieved {FeatureCount} feature IDs for plan {PlanId}", 
                        featureIds.Count, subscriptionPlanId);
                    
                    return featureIds;
                }
                else
                {
                    _logger.LogWarning("Failed to get feature IDs for plan {PlanId}. Status: {StatusCode}", 
                        subscriptionPlanId, response.StatusCode);
                    return new List<Guid>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching feature IDs for plan {PlanId}", subscriptionPlanId);
                return new List<Guid>();
            }
        }

        public async Task<Dictionary<Guid, SubscriptionFeatureDetails>> GetFeatureFlagDetailsByIdsAsync(List<Guid> featureIds)
        {
            try
            {
                if (!featureIds.Any())
                {
                    return new Dictionary<Guid, SubscriptionFeatureDetails>();
                }

                _logger.LogInformation("Fetching feature flag details for {FeatureCount} features", featureIds.Count);

                var featureIdsQuery = string.Join(",", featureIds);
                var response = await _httpClient.GetAsync($"{_subscriptionServiceBaseUrl}/api/feature-flags/by-ids?featureIds={featureIdsQuery}");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var featureFlags = JsonSerializer.Deserialize<List<FeatureFlagResponse>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    var result = featureFlags?.ToDictionary(
                        ff => ff.FeatureId,
                        ff => new SubscriptionFeatureDetails
                        {
                            FeatureId = ff.FeatureId,
                            FeatureName = ff.Name,
                            MenuId = ff.MenuId
                        }) ?? new Dictionary<Guid, SubscriptionFeatureDetails>();
                    
                    _logger.LogInformation("Successfully retrieved {FeatureFlagCount} feature flag details", result.Count);
                    
                    return result;
                }
                else
                {
                    _logger.LogWarning("Failed to get feature flag details. Status: {StatusCode}", response.StatusCode);
                    return new Dictionary<Guid, SubscriptionFeatureDetails>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching feature flag details for {FeatureCount} features", featureIds.Count);
                return new Dictionary<Guid, SubscriptionFeatureDetails>();
            }
        }
    }

    // Response DTOs for subscription service integration
    public class SubscriptionPlanResponse
    {
        public Guid PlanId { get; set; }
    }

    public class PlanFeatureResponse
    {
        public Guid FeatureId { get; set; }
        public string Name { get; set; } = string.Empty;
        public bool IsEnabled { get; set; }
    }

    public class FeatureFlagResponse
    {
        public Guid FeatureId { get; set; }
        public string Name { get; set; } = string.Empty;
        public Guid? MenuId { get; set; }
    }
}
