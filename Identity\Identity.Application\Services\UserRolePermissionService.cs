using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Identity.Application.Common.Interfaces;
using Identity.Application.Common.Models;
using Identity.Domain.Entities;

namespace Identity.Application.Services
{
    public class UserRolePermissionService : IUserRolePermissionService
    {
        private readonly IApplicationDbContext _context;
        private readonly Domain.Repositories.IRoleRepository _roleRepository;
        private readonly Domain.Repositories.IPermissionRepository _permissionRepository;
        private readonly IFeatureIntegrationService _featureIntegrationService;
        private readonly IMenuService _menuService;
        private readonly ISubscriptionIntegrationService _subscriptionIntegrationService;
        private readonly ILogger<UserRolePermissionService> _logger;

        public UserRolePermissionService(
            IApplicationDbContext context,
            Domain.Repositories.IRoleRepository roleRepository,
            Domain.Repositories.IPermissionRepository permissionRepository,
            IFeatureIntegrationService featureIntegrationService,
            IMenuService menuService,
            ISubscriptionIntegrationService subscriptionIntegrationService,
            ILogger<UserRolePermissionService> logger)
        {
            _context = context;
            _roleRepository = roleRepository;
            _permissionRepository = permissionRepository;
            _featureIntegrationService = featureIntegrationService;
            _menuService = menuService;
            _subscriptionIntegrationService = subscriptionIntegrationService;
            _logger = logger;
        }

        public async Task<List<RoleDto>> GetUserRolesWithPermissionsAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Fetching roles and permissions for user {UserId}", userId);

                // Get user role IDs
                var roleIds = await GetUserRoleIdsAsync(userId);
                if (!roleIds.Any())
                {
                    _logger.LogInformation("No roles found for user {UserId}", userId);
                    return new List<RoleDto>();
                }

                var roles = new List<RoleDto>();

                foreach (var roleId in roleIds)
                {
                    // Get role details
                    var role = await _roleRepository.GetByIdAsync(roleId);
                    if (role == null)
                    {
                        _logger.LogWarning("Role {RoleId} not found", roleId);
                        continue;
                    }

                    // Get permission IDs for this role
                    var permissionIds = await GetRolePermissionIdsAsync(roleId);

                    // Get permission details with complete data
                    var permissions = await GetCompletePermissionsByIdsAsync(permissionIds);

                    var roleDto = new RoleDto
                    {
                        RoleId = role.Id.ToString(),
                        RoleName = role.Name,
                        Permissions = permissions
                    };

                    roles.Add(roleDto);
                }

                _logger.LogInformation("Successfully fetched {RoleCount} roles with permissions for user {UserId}",
                    roles.Count, userId);

                return roles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching roles and permissions for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<RoleDto>> GetUserRolesWithPermissionsByUserTypeAsync(Guid userId, UserType userType)
        {
            try
            {
                _logger.LogInformation("Fetching roles and permissions for user {UserId} with user type {UserType}", userId, userType);

                // Check if this is a subscription-based user type
                if (IsSubscriptionBasedUserType(userType))
                {
                    return await GetSubscriptionBasedRolesAsync(userId);
                }
                // Check if this is a role-based user type
                else if (IsRoleBasedUserType(userType))
                {
                    return await GetRoleBasedRolesAsync(userId);
                }
                else
                {
                    // For other user types, return empty roles
                    _logger.LogInformation("User type {UserType} does not require role/permission data", userType);
                    return new List<RoleDto>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching roles and permissions for user {UserId} with user type {UserType}", userId, userType);
                return new List<RoleDto>();
            }
        }

        public async Task<List<Guid>> GetUserRoleIdsAsync(Guid userId)
        {
            try
            {
                var roleIds = await _context.UserRoles
                    .Where(ur => ur.UserId == userId)
                    .Select(ur => ur.RoleId)
                    .ToListAsync();

                _logger.LogDebug("Found {RoleCount} roles for user {UserId}", roleIds.Count, userId);
                return roleIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching role IDs for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<Guid>> GetRolePermissionIdsAsync(Guid roleId)
        {
            try
            {
                var permissionIds = await _context.RolePermissions
                    .Where(rp => rp.RoleId == roleId)
                    .Select(rp => rp.PermissionId)
                    .ToListAsync();

                _logger.LogDebug("Found {PermissionCount} permissions for role {RoleId}",
                    permissionIds.Count, roleId);
                return permissionIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching permission IDs for role {RoleId}", roleId);
                throw;
            }
        }

        public async Task<List<PermissionDto>> GetPermissionsByIdsAsync(List<Guid> permissionIds)
        {
            try
            {
                if (!permissionIds.Any())
                {
                    return new List<PermissionDto>();
                }

                var permissions = await _context.Permissions
                    .Where(p => permissionIds.Contains(p.Id))
                    .ToListAsync();

                var permissionDtos = permissions.Select(p => new PermissionDto
                {
                    MenuId = string.Empty, // Will be populated later from subscription service
                    MenuName = string.Empty, // Will be populated later from menu service
                    FeatureId = p.FeatureId?.ToString() ?? string.Empty,
                    FeatureName = string.Empty, // Will be populated later from subscription service
                    PermissionName = p.PermissionName ?? p.Name,
                    Action = p.Action ?? string.Empty
                }).ToList();

                _logger.LogDebug("Converted {PermissionCount} permissions to DTOs", permissionDtos.Count);
                return permissionDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching permissions by IDs");
                throw;
            }
        }

        public async Task<List<PermissionDto>> GetCompletePermissionsByIdsAsync(List<Guid> permissionIds)
        {
            try
            {
                if (!permissionIds.Any())
                {
                    return new List<PermissionDto>();
                }

                _logger.LogInformation("Fetching complete permission details for {PermissionCount} permissions",
                    permissionIds.Count);

                // Get basic permission data
                var permissions = await _context.Permissions
                    .Where(p => permissionIds.Contains(p.Id))
                    .ToListAsync();

                if (!permissions.Any())
                {
                    _logger.LogWarning("No permissions found for the provided IDs");
                    return new List<PermissionDto>();
                }

                // Extract unique feature IDs
                var featureIds = permissions
                    .Where(p => p.FeatureId.HasValue)
                    .Select(p => p.FeatureId!.Value.ToString())
                    .Distinct()
                    .ToList();

                // Get feature details from subscription service
                var featureDetails = await _featureIntegrationService.GetFeatureDetailsByIdsAsync(featureIds);

                // Extract all menu IDs from feature details
                var allMenuIds = featureDetails.Values
                    .SelectMany(f => f.MenuIds)
                    .Distinct()
                    .ToList();

                // Get menu names
                var menuNames = await _menuService.GetMenuNamesByIdsAsync(allMenuIds);

                // Build complete permission DTOs
                var permissionDtos = new List<PermissionDto>();

                foreach (var permission in permissions)
                {
                    var featureId = permission.FeatureId?.ToString() ?? string.Empty;
                    var featureDetail = featureDetails.ContainsKey(featureId) ? featureDetails[featureId] : null;

                    if (featureDetail != null && featureDetail.MenuIds.Any())
                    {
                        // Create one permission DTO for each menu associated with the feature
                        foreach (var menuId in featureDetail.MenuIds)
                        {
                            var menuName = menuNames.ContainsKey(menuId) ? menuNames[menuId] : string.Empty;

                            permissionDtos.Add(new PermissionDto
                            {
                                MenuId = menuId,
                                MenuName = menuName,
                                FeatureId = featureId,
                                FeatureName = featureDetail.FeatureName,
                                PermissionName = permission.PermissionName ?? permission.Name,
                                Action = permission.Action ?? string.Empty
                            });
                        }
                    }
                    else
                    {
                        // Create permission DTO without menu information
                        permissionDtos.Add(new PermissionDto
                        {
                            MenuId = string.Empty,
                            MenuName = string.Empty,
                            FeatureId = featureId,
                            FeatureName = featureDetail?.FeatureName ?? string.Empty,
                            PermissionName = permission.PermissionName ?? permission.Name,
                            Action = permission.Action ?? string.Empty
                        });
                    }
                }

                _logger.LogInformation("Successfully built {PermissionDtoCount} complete permission DTOs from {PermissionCount} permissions",
                    permissionDtos.Count, permissions.Count);

                return permissionDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching complete permissions by IDs");
                throw;
            }
        }

        private static bool IsSubscriptionBasedUserType(UserType userType)
        {
            return userType == UserType.Carrier ||
                   userType == UserType.TransportCompany ||
                   userType == UserType.Broker ||
                   userType == UserType.ShipperCompany ||
                   userType == UserType.ShipperIndividual;
        }

        private static bool IsRoleBasedUserType(UserType userType)
        {
            return userType == UserType.umsTransportCompany ||
                   userType == UserType.umsCarrierCompany ||
                   userType == UserType.umsShipperCompany;
        }

        private async Task<List<RoleDto>> GetSubscriptionBasedRolesAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Processing subscription-based roles for user {UserId}", userId);

                // Step 1: Get user's subscription plan ID
                var subscriptionPlanId = await _subscriptionIntegrationService.GetUserSubscriptionPlanIdAsync(userId);
                if (!subscriptionPlanId.HasValue)
                {
                    _logger.LogInformation("No subscription plan found for user {UserId}", userId);
                    return new List<RoleDto>();
                }

                // Step 2: Get feature IDs from PlanFeature table
                var featureIds = await _subscriptionIntegrationService.GetPlanFeatureIdsAsync(subscriptionPlanId.Value);
                if (!featureIds.Any())
                {
                    _logger.LogInformation("No features found for subscription plan {PlanId}", subscriptionPlanId.Value);
                    return new List<RoleDto>();
                }

                // Step 3: Get feature details from FeatureFlag table
                var featureDetails = await _subscriptionIntegrationService.GetFeatureFlagDetailsByIdsAsync(featureIds);

                // Step 4: Get menu names for all menu IDs
                var menuIds = featureDetails.Values
                    .Where(f => f.MenuId.HasValue)
                    .Select(f => f.MenuId.Value.ToString())
                    .Distinct()
                    .ToList();

                var menuNames = await _menuService.GetMenuNamesByIdsAsync(menuIds);

                // Step 5: Get permissions from Permissions table based on feature IDs
                var permissions = await GetPermissionsByFeatureIdsAsync(featureIds);

                // Step 6: Build permission DTOs
                var permissionDtos = BuildSubscriptionPermissionDtos(featureDetails, menuNames, permissions);

                // Return single role with all permissions (subscription-based users don't have role names)
                return new List<RoleDto>
                {
                    new RoleDto
                    {
                        RoleId = string.Empty,
                        RoleName = string.Empty,
                        Permissions = permissionDtos
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing subscription-based roles for user {UserId}", userId);
                return new List<RoleDto>();
            }
        }

        private async Task<List<RoleDto>> GetRoleBasedRolesAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Processing role-based roles for user {UserId}", userId);

                // Step 1: Get user role IDs from UserRoles table
                var roleIds = await GetUserRoleIdsAsync(userId);
                if (!roleIds.Any())
                {
                    _logger.LogInformation("No roles found for user {UserId}", userId);
                    return new List<RoleDto>();
                }

                var roles = new List<RoleDto>();

                foreach (var roleId in roleIds)
                {
                    // Step 2: Get role details
                    var role = await _roleRepository.GetByIdAsync(roleId);
                    if (role == null)
                    {
                        _logger.LogWarning("Role {RoleId} not found", roleId);
                        continue;
                    }

                    // Step 3: Get permission IDs from RolePermissions table
                    var permissionIds = await GetRolePermissionIdsAsync(roleId);
                    if (!permissionIds.Any())
                    {
                        _logger.LogInformation("No permissions found for role {RoleId}", roleId);
                        continue;
                    }

                    // Step 4: Get feature IDs from Permissions table
                    var featureIds = await GetFeatureIdsByPermissionIdsAsync(permissionIds);

                    // Step 5: Get feature details from FeatureFlag table in subscription service
                    var featureDetails = await _subscriptionIntegrationService.GetFeatureFlagDetailsByIdsAsync(featureIds);

                    // Step 6: Get menu names
                    var menuIds = featureDetails.Values
                        .Where(f => f.MenuId.HasValue)
                        .Select(f => f.MenuId.Value.ToString())
                        .Distinct()
                        .ToList();

                    var menuNames = await _menuService.GetMenuNamesByIdsAsync(menuIds);

                    // Step 7: Get permission details
                    var permissions = await GetPermissionsByIdsAsync(permissionIds);

                    // Step 8: Build permission DTOs
                    var permissionDtos = BuildRoleBasedPermissionDtos(featureDetails, menuNames, permissions);

                    var roleDto = new RoleDto
                    {
                        RoleId = role.Id.ToString(),
                        RoleName = role.Name,
                        Permissions = permissionDtos
                    };

                    roles.Add(roleDto);
                }

                _logger.LogInformation("Successfully processed {RoleCount} roles for user {UserId}", roles.Count, userId);
                return roles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing role-based roles for user {UserId}", userId);
                return new List<RoleDto>();
            }
        }

        private async Task<List<Permission>> GetPermissionsByFeatureIdsAsync(List<Guid> featureIds)
        {
            try
            {
                if (!featureIds.Any())
                {
                    return new List<Permission>();
                }

                var permissions = await _context.Permissions
                    .Where(p => p.FeatureId.HasValue && featureIds.Contains(p.FeatureId.Value))
                    .ToListAsync();

                _logger.LogDebug("Found {PermissionCount} permissions for {FeatureCount} features",
                    permissions.Count, featureIds.Count);

                return permissions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching permissions by feature IDs");
                return new List<Permission>();
            }
        }

        private async Task<List<Guid>> GetFeatureIdsByPermissionIdsAsync(List<Guid> permissionIds)
        {
            try
            {
                if (!permissionIds.Any())
                {
                    return new List<Guid>();
                }

                var featureIds = await _context.Permissions
                    .Where(p => permissionIds.Contains(p.Id) && p.FeatureId.HasValue)
                    .Select(p => p.FeatureId!.Value)
                    .Distinct()
                    .ToListAsync();

                _logger.LogDebug("Found {FeatureCount} feature IDs for {PermissionCount} permissions",
                    featureIds.Count, permissionIds.Count);

                return featureIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching feature IDs by permission IDs");
                return new List<Guid>();
            }
        }

        private List<PermissionDto> BuildSubscriptionPermissionDtos(
            Dictionary<Guid, SubscriptionFeatureDetails> featureDetails,
            Dictionary<string, string> menuNames,
            List<Permission> permissions)
        {
            var permissionDtos = new List<PermissionDto>();

            foreach (var permission in permissions)
            {
                var featureId = permission.FeatureId;
                if (!featureId.HasValue || !featureDetails.ContainsKey(featureId.Value))
                {
                    continue;
                }

                var feature = featureDetails[featureId.Value];
                var menuId = feature.MenuId?.ToString() ?? string.Empty;
                var menuName = !string.IsNullOrEmpty(menuId) && menuNames.ContainsKey(menuId)
                    ? menuNames[menuId]
                    : string.Empty;

                permissionDtos.Add(new PermissionDto
                {
                    MenuId = menuId,
                    MenuName = menuName,
                    FeatureId = featureId.Value.ToString(),
                    FeatureName = feature.FeatureName,
                    PermissionName = permission.PermissionName ?? permission.Name,
                    Action = permission.Action ?? string.Empty
                });
            }

            return permissionDtos;
        }

        private List<PermissionDto> BuildRoleBasedPermissionDtos(
            Dictionary<Guid, SubscriptionFeatureDetails> featureDetails,
            Dictionary<string, string> menuNames,
            List<PermissionDto> permissions)
        {
            var permissionDtos = new List<PermissionDto>();

            foreach (var permission in permissions)
            {
                if (!Guid.TryParse(permission.FeatureId, out var featureId) || !featureDetails.ContainsKey(featureId))
                {
                    // Add permission without feature/menu details
                    permissionDtos.Add(new PermissionDto
                    {
                        MenuId = string.Empty,
                        MenuName = string.Empty,
                        FeatureId = permission.FeatureId,
                        FeatureName = string.Empty,
                        PermissionName = permission.PermissionName,
                        Action = permission.Action
                    });
                    continue;
                }

                var feature = featureDetails[featureId];
                var menuId = feature.MenuId?.ToString() ?? string.Empty;
                var menuName = !string.IsNullOrEmpty(menuId) && menuNames.ContainsKey(menuId)
                    ? menuNames[menuId]
                    : string.Empty;

                permissionDtos.Add(new PermissionDto
                {
                    MenuId = menuId,
                    MenuName = menuName,
                    FeatureId = permission.FeatureId,
                    FeatureName = feature.FeatureName,
                    PermissionName = permission.PermissionName,
                    Action = permission.Action
                });
            }

            return permissionDtos;
        }
    }
}
