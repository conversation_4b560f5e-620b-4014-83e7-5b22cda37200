# Role and Permission Implementation for Login APIs

## Overview

This document describes the implementation of role and permission logic for the `VerifyOtpLogin` and `MobilePasswordLogin` APIs. The implementation handles two different user type scenarios with different data flow patterns.

## User Type Scenarios

### Scenario 1: Subscription-based User Types
**User Types**: `Carrier`, `TransportCompany`, `Broker`, `ShipperCompany`, `ShipperIndividual`

**Data Flow**:
1. Get user's subscription plan ID from subscription service
2. Fetch feature IDs from `PlanFeature` table in subscription service
3. Get feature details (name, menu ID) from `FeatureFlag` table in subscription service
4. Get menu names from `Menus` table in identity service
5. Get permissions from `Permissions` table in identity service based on feature IDs

**Response Structure**:
```json
{
  "roles": [
    {
      "roleId": "",
      "roleName": "",
      "permissions": [
        {
          "menuId": "guid",
          "menuName": "Menu Name",
          "featureId": "guid",
          "featureName": "Feature Name",
          "permissionName": "permission_name",
          "action": "action_name"
        }
      ]
    }
  ]
}
```

### Scenario 2: Role-based User Types
**User Types**: `umsTransportCompany`, `umsCarrierCompany`, `umsShipperCompany`

**Data Flow**:
1. Get role IDs from `UserRoles` table using user ID
2. Get permission IDs from `RolePermissions` table using role IDs
3. Get feature IDs from `Permissions` table using permission IDs
4. Get feature details from `FeatureFlag` table in subscription service using feature IDs
5. Get menu names from `Menus` table in identity service using menu IDs

**Response Structure**:
```json
{
  "roles": [
    {
      "roleId": "guid",
      "roleName": "Role Name",
      "permissions": [
        {
          "menuId": "guid",
          "menuName": "Menu Name",
          "featureId": "guid",
          "featureName": "Feature Name",
          "permissionName": "permission_name",
          "action": "action_name"
        }
      ]
    }
  ]
}
```

## Implementation Details

### New Services Created

1. **ISubscriptionIntegrationService** - Handles integration with subscription service
   - `GetUserSubscriptionPlanIdAsync()` - Gets user's subscription plan
   - `GetPlanFeatureIdsAsync()` - Gets features for a plan
   - `GetFeatureFlagDetailsByIdsAsync()` - Gets feature details

2. **Enhanced UserRolePermissionService** - Added new method
   - `GetUserRolesWithPermissionsByUserTypeAsync()` - Routes to appropriate logic based on user type

### Modified Files

1. **VerifyOtpLoginCommandHandler.cs** - Added role/permission logic
2. **MobilePasswordLoginCommandHandler.cs** - Updated to use new method
3. **UserRolePermissionService.cs** - Added subscription and role-based logic
4. **IUserRolePermissionService.cs** - Added new method signature
5. **DependencyInjection.cs** - Registered new services

### Error Handling

- If subscription service is unavailable, returns empty roles list
- If role/permission data is missing, continues with empty permissions
- Logs all errors but doesn't fail the login process
- Null/empty checks for all data to prevent exceptions

### Configuration

Add to appsettings.json:
```json
{
  "Services": {
    "SubscriptionService": {
      "BaseUrl": "https://localhost:7001"
    }
  }
}
```

## Testing

Basic unit tests are provided in `RolePermissionIntegrationTests.cs` to verify:
- Subscription-based user types return correct structure
- Role-based user types return correct structure  
- Other user types return empty roles list

## Notes

- Empty/null values in response fields are handled gracefully
- The implementation is backward compatible with existing login functionality
- Performance is optimized with batch operations where possible
- Caching can be added to subscription service calls if needed
